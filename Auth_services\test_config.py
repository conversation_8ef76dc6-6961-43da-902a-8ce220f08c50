#!/usr/bin/env python3
"""
Script test config.py để kiểm tra lỗi settings = Settings()
"""

import sys
import os

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_config_import():
    """Test import config và khởi tạo Settings"""
    print("🔍 Kiểm tra import config...")
    try:
        from core.config import Settings
        print("✅ Import Settings class thành công!")
        return True
    except Exception as e:
        print(f"❌ Lỗi import Settings: {e}")
        return False

def test_settings_creation():
    """Test tạo instance Settings()"""
    print("\n🔍 Kiểm tra tạo Settings() instance...")
    try:
        from core.config import Settings
        
        # Tạo instance Settings
        settings = Settings()
        print("✅ Tạo Settings() thành công!")
        
        # Kiểm tra các thuộc tính
        print(f"📋 Thông tin config:")
        print(f"   - DB_USER: {settings.DB_USER}")
        print(f"   - DB_HOST: {settings.DB_HOST}")
        print(f"   - DB_PORT: {settings.DB_PORT}")
        print(f"   - DB_NAME: {settings.DB_NAME}")
        print(f"   - DB_URL: {settings.get_db_url}")
        
        return True
    except Exception as e:
        print(f"❌ Lỗi tạo Settings(): {e}")
        return False

def test_existing_settings():
    """Test import settings đã được tạo sẵn"""
    print("\n🔍 Kiểm tra import settings instance...")
    try:
        from core.config import settings
        print("✅ Import settings instance thành công!")
        
        # Test truy cập thuộc tính
        db_url = settings.get_db_url
        print(f"📊 Database URL: {db_url}")
        
        return True
    except Exception as e:
        print(f"❌ Lỗi import settings: {e}")
        return False

def test_env_file_path():
    """Test đường dẫn file .env"""
    print("\n🔍 Kiểm tra đường dẫn file .env...")
    try:
        import os
        
        # Đường dẫn theo config.py
        config_dir = os.path.join(os.path.dirname(__file__), 'app', 'core')
        env_path = os.path.join(config_dir, "..", ".env")
        env_path = os.path.abspath(env_path)
        
        print(f"📁 Đường dẫn .env theo config: {env_path}")
        
        if os.path.exists(env_path):
            print("✅ File .env tồn tại!")
            
            # Đọc nội dung file
            with open(env_path, 'r') as f:
                content = f.read()
            print("📄 Nội dung file .env:")
            print(content)
            
            return True
        else:
            print("❌ File .env không tồn tại!")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi kiểm tra file .env: {e}")
        return False

def main():
    """Hàm chính"""
    print("🚀 Bắt đầu kiểm tra config.py...")
    print("=" * 60)
    
    tests = [
        ("Import Config", test_config_import),
        ("Settings Creation", test_settings_creation),
        ("Existing Settings", test_existing_settings),
        ("Env File Path", test_env_file_path)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Lỗi trong test {test_name}: {e}")
            results.append((test_name, False))
    
    # Tổng kết
    print("\n" + "=" * 60)
    print("📋 KẾT QUẢ KIỂM TRA CONFIG:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"   - {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 CONFIG HOẠT ĐỘNG HOÀN HẢO!")
        print("💡 settings = Settings() không còn lỗi!")
    else:
        print("\n⚠️  VẪN CÒN VẤN ĐỀ!")
        print("💡 Vui lòng kiểm tra lại!")

if __name__ == "__main__":
    main()
