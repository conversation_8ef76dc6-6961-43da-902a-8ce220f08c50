from pydantic_settings import BaseSettings
import os 
from typing import List

class Settings(BaseSettings): 
    
    #kết nối database 
    DB_USER: str
    DB_PASSWORD: str 
    DB_HOST: str 
    DB_PORT: str
    DB_NAME: str = "auth_service+db"
    DB_URL: str = "" 

    @property 
    def get_db_url(self)  -> str: 
        if self.DB_URL: 
            return self.DB_URL
        return f"postgresql+psycopg2://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_HOST}/{self.DB_NAME}"
    
    #CORS 
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000"]

    #config 
    class config: 
        env_file = os.path.join(os.path.dirname(__file__), "..", ".env")
        case_sensitive = True
        env_file_encoding = 'utf-8'

settings = Settings()