#!/usr/bin/env python3
"""
Script kiểm tra database setup và SQLAlchemy configuration
"""

import sys
import os

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_database_imports():
    """Test import các module database"""
    print("🔍 Kiểm tra import database modules...")
    try:
        from core.database import engine, SessionLocal, Base, get_db, test_connection
        from core.config import settings
        print("✅ Import database modules thành công!")
        return True
    except ImportError as e:
        print(f"❌ Lỗi import: {e}")
        return False

def test_database_connection():
    """Test kết nối database qua SQLAlchemy"""
    print("\n🔍 Kiểm tra kết nối database...")
    try:
        from core.database import test_connection
        if test_connection():
            print("✅ Kết nối database thành công!")
            return True
        else:
            print("❌ Kết nối database thất bại!")
            return False
    except Exception as e:
        print(f"❌ Lỗi kiểm tra kết nối: {e}")
        return False

def test_session_creation():
    """Test tạo database session"""
    print("\n🔍 Kiểm tra tạo database session...")
    try:
        from core.database import SessionLocal
        
        # Tạo session
        db = SessionLocal()
        print("✅ Tạo session thành công!")
        
        # Test query đơn giản
        from sqlalchemy import text
        result = db.execute(text("SELECT current_database(), current_user"))
        row = result.fetchone()
        print(f"📊 Database: {row[0]}, User: {row[1]}")
        
        # Đóng session
        db.close()
        print("✅ Đóng session thành công!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi tạo session: {e}")
        return False

def test_get_db_generator():
    """Test generator function get_db()"""
    print("\n🔍 Kiểm tra get_db() generator...")
    try:
        from core.database import get_db
        
        # Test generator
        db_gen = get_db()
        db = next(db_gen)
        
        # Test query
        from sqlalchemy import text
        result = db.execute(text("SELECT 1 as test"))
        row = result.fetchone()
        print(f"✅ Generator test thành công! Kết quả: {row[0]}")
        
        # Cleanup generator
        try:
            next(db_gen)
        except StopIteration:
            print("✅ Generator cleanup thành công!")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi generator: {e}")
        return False

def main():
    """Hàm chính chạy tất cả tests"""
    print("🚀 Bắt đầu kiểm tra database setup...")
    print("=" * 60)
    
    tests = [
        ("Import Modules", test_database_imports),
        ("Database Connection", test_database_connection),
        ("Session Creation", test_session_creation),
        ("Get DB Generator", test_get_db_generator)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Lỗi trong test {test_name}: {e}")
            results.append((test_name, False))
    
    # Tổng kết
    print("\n" + "=" * 60)
    print("📋 KẾT QUẢ KIỂM TRA DATABASE SETUP:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"   - {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 TẤT CẢ TESTS THÀNH CÔNG!")
        print("💡 Database setup hoàn tất, sẵn sàng tạo models!")
    else:
        print("\n⚠️  CÓ TESTS THẤT BẠI!")
        print("💡 Vui lòng kiểm tra lại cấu hình!")

if __name__ == "__main__":
    main()
