

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator

from .config import settings


engine = create_engine(
    settings.get_db_url,
    pool_pre_ping=True,  # Kiểm tra kết nối trư<PERSON><PERSON> khi sử dụng
    pool_recycle=300,    # Tái tạo connection sau 5 phút
    pool_size=5,         # Số connection trong pool
    max_overflow=10      # Số connection tối đa có thể tạo thêm
)

# Tạo SessionLocal class
SessionLocal = sessionmaker(
    autocommit=False,    # Không tự động commit
    autoflush=False,     # Không tự động flush
    bind=engine          # Bind với engine
)

Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

